#!/bin/sh
# Create conf.d folder if not exists
mkdir -p /etc/nginx/conf.d

# Substitute env vars into default.conf.template
envsubst < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf

# Optional: Show the final config for debugging
echo "======= Final Nginx Config ======="
cat /etc/nginx/conf.d/default.conf
echo "=================================="

# Start nginx
exec nginx -g 'daemon off;'